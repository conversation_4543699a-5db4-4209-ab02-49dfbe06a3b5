<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const router = useRouter()
const dialogVisible = ref(false)

const toRouter = (name: string) => {
    router.push({
        name,
    })
}

const store = useStore<RootState>()
const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

const toHelp = () => {
    const oemStorage = sessionStorage.getItem('oemConfig')
    
    if(oemStorage && JSON.parse(oemStorage).helpManualAddress){
        if(oemStorage && JSON.parse(oemStorage) && JSON.parse(oemStorage).modules.length > 0 && JSON.parse(oemStorage).modules[0].config.helpManualAddress){
            return window.open(JSON.parse(oemStorage).modules[0].config.helpManualAddress, '_blank')
        }else{
            return window.open(`https://${JSON.parse(oemStorage).modules[0].config.helpManualAddress}`, '_blank')
        }
    }else if(oemInfo?.value?.helpManualAddress){
        if(oemInfo.value.helpManualAddress.includes('http')){
            return window.open(oemInfo.value.helpManualAddress, '_blank')
        }else{
            return window.open(`https://${oemInfo.value.helpManualAddress}`, '_blank')
        }
    }else{
        window.open('https://njshuzutech.yuque.com/yaur6d/fle22z/zme5wzvc01gg73qi?singleDoc', '_blank')
    }
}

const showCustomerLine = () => {
    dialogVisible.value = true
}
</script>

<template>
    <div class="flex flex-column gap-8">
        <div
            class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-8 border-radius-4 pointer"
            @click="toRouter('profile')"
        >
            <!-- <Icon icon="icon-a-yonghuzhongxinno" size="16" /> -->
            <el-icon :size="16" color="#303133"><User /></el-icon>
            <div class="color-black font-16 lh-16">用户中心</div>
        </div>
        <div
            class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-8 border-radius-4 pointer"
            @click="toRouter('notification')"
        >
            <!-- <Icon icon="icon-a-xiaoxitongzhino" size="16" /> -->
            <el-icon :size="16" color="#303133"><Message /></el-icon>
            <div class="color-black font-16 lh-16">消息中心</div>
        </div>
        <div
            class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-8 border-radius-4 pointer"
            @click="toHelp()"
        >
            <!-- <Icon icon="icon-a-xiaoxitongzhino" size="16" /> -->
            <el-icon :size="16" color="#303133"><Collection /></el-icon>
            <div class="color-black font-16 lh-16">操作手册</div>
        </div>
        <div
            class="flex flex-row top-bottom-center gap-8 back-tag-bg--hover all-padding-8 border-radius-4 pointer"
            @click="showCustomerLine()"
        >
            <!-- <Icon icon="icon-a-xiaoxitongzhino" size="16" /> -->
            <el-icon :size="16" color="#303133"><Service /></el-icon>
            <div class="color-black font-16 lh-16">客服电话</div>
        </div>

        <el-dialog v-model="dialogVisible" title="客服电话" width="320">
            <template #default>
                <div class="flex flex-column top-bottom-center left-right-center all-padding-16 gap-16">
                    <div>请拨打我们的客服电话，我们全心为您服务，满足您的需求。臻企云，您的卓越服务伙伴！</div>
                    <div class="font-26 color-blue font-weight-500">400-100-0086</div>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped></style>
