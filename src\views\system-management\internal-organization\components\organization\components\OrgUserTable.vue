<script setup lang="ts">
import systemService from '@/service/systemService'
import type { IPageUserItem, IUserListRequestParams, IUserPageListRequest } from '@/types/user'
import { computed, onMounted, ref } from 'vue'
import TableFilter from './TableFilter.vue'
import type { IOrgTreeItem } from '@/types/org'
import type { IRoleItem } from '@/types/role'
import UserUpdateDialog from './UserUpdateDialog.vue'
import { ElMessage } from 'element-plus'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { downloadFile } from '@/utils/download'
import permissionService from '@/service/permissionService'

const props = defineProps<{
    orgList: IOrgTreeItem[]
    currentOrg: IOrgTreeItem | null
    setCurrentOrg: (v: IOrgTreeItem | null) => void
}>()

const roleList = ref<IRoleItem[]>([])
const multipleSelection = ref<IPageUserItem[]>([])
const list = ref<IPageUserItem[]>([])
const loading = ref(false)
const dialogVisible = ref(false)
const updateType = ref('add')
const currentUser = ref<IPageUserItem | null>(null)
const pageInfo = ref({
    page: 1,
    pageSize: 10,
    total: 0,
})
const store = useStore<RootState>()
const exporting = ref(false)

const isPlatManager = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    const { tenantId } = user || {}
    return tenantId === ''
})

const isYunwei = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user?.role?.includes('yunwei')
})

const currentParams = ref<IUserPageListRequest>()

const getData = (requestParams?: IUserListRequestParams) => {
    if (loading.value) return

    if (isPlatManager.value && !props.currentOrg) return

    let params: IUserPageListRequest = {
        page: pageInfo.value.page,
        pageSize: pageInfo.value.pageSize,
    }

    if (requestParams) {
        params = { ...params, ...requestParams }
        currentParams.value = params
    } else {
        if (currentParams.value) {
            params = { ...params, ...currentParams.value }
        }
    }

    params = {
        ...params,
        page: pageInfo.value.page,
        pageSize: pageInfo.value.pageSize,
    }

    loading.value = true

    systemService
        .userPage(params)
        .then((res) => {
            const { errCode, data, total } = res
            if (errCode === 0) {
                list.value = data
                pageInfo.value.total = total
            } else {
                pageInfo.value.total = 0
                list.value = []
            }
        })
        .catch(() => {
            pageInfo.value.total = 0
            list.value = []
        })
        .finally(() => {
            loading.value = false
        })
}
const handleSelectionChange = (val: IPageUserItem[]) => {
    console.log('val', val)
    multipleSelection.value = val
}

const getRoleList = () => {
    systemService.roleListByName({}).then((res) => {
        const { errCode, data } = res
        if (errCode === 0) {
            roleList.value = data
        } else {
            list.value = []
        }
    })
}

const addUser = () => {
    if (isPlatManager.value && !props.currentOrg) {
        ElMessage.warning('请先选择一个组织')
        return
    }
    updateType.value = 'add'
    dialogVisible.value = true
}

const editUser = (user: IPageUserItem) => {
    updateType.value = 'edit'
    dialogVisible.value = true
    currentUser.value = user
}

const onUpdateDialogVisible = (refresh?: boolean) => {
    dialogVisible.value = false
    currentUser.value = null
    if (refresh) {
        getData()
    }
}

const changeStatus = (status: number, user: IPageUserItem) => {
    systemService
        .userEdit({
            id: user.id,
            nickname: user.nickname,
            orgId: user.orgId,
            role: user.role,
            status: status.toString(),
            username: user.username,
        })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode !== 0) {
                ElMessage.error(errMsg || '操作失败')
            } else {
                getData()
            }
        })
        .catch((err) => {
            ElMessage.error(err || '操作失败，请稍后再试')
        })
}

const disableSelected = () => {
    if (multipleSelection.value.length === 0) {
        ElMessage.warning('请选择要操作的用户')
        return
    }
    systemService
        .userDisable({
            ids: multipleSelection.value.map((item) => item.id),
        })
        .then(() => {
            getData()
        })
}

const handleSetCurrentOrg = (org: IOrgTreeItem | null) => {
    if (org) {
        props.setCurrentOrg(org)
    } else {
        props.setCurrentOrg(null)
        if (isPlatManager.value) {
            pageInfo.value.page = 1
            pageInfo.value.total = 0
            list.value = []
        }
    }
}

const exportUser = () => {
    if (pageInfo.value.total === 0) {
        ElMessage.warning('没有可导出的用户')
        return
    }

    if (exporting.value) return

    exporting.value = true
    systemService
        .userExportUser({
            mobile: currentParams.value?.mobile || '',
            orgId: currentParams.value?.orgId || '',
            roleId: currentParams.value?.roleId || '',
            status: currentParams.value?.status || '0',
            username: currentParams.value?.username || '',
        })
        .then((res) => {
            exporting.value = false
            downloadFile(res)
        })
        .catch(() => {
            ElMessage.warning('导出失败，请稍后再试')
        })
}

onMounted(() => {
    if (isPlatManager.value) return
    getData()
    getRoleList()
})
</script>

<template>
    <div class="org-user-table flex flex-column gap-16 flex-1">
        <TableFilter
            :org-list="orgList"
            :get-data="getData"
            :current-org="currentOrg"
            :set-current-org="handleSetCurrentOrg"
        />
        <div class="flex flex-row gap-16 space-between tb-padding-16">
            <div class="flex flex-row gap-16 top-bottom-center">
                <div class="flex flex-row gap-8 top-bottom-center">
                    <div class="font-14">
                        已选<span class="color-blue lr-padding-2 font-weight-600">{{ multipleSelection.length }}</span
                        >个
                    </div>
                </div>
                <div class="flex flex-row">
                    <el-button @click="disableSelected"> 批量停用 </el-button>
                    <el-button @click="exportUser" :loading="exporting"> 导出 </el-button>
                </div>
            </div>
            <div v-if="permissionService.isUserAddPermitted()" class="flex flex-row gap-16 top-bottom-center">
                <div class="flex flex-row gap-16">
                    <el-button type="primary" @click="addUser"> 新增用户 </el-button>
                </div>
            </div>
        </div>
        <el-table
            :data="list"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            show-overflow-tooltip
            row-key="id"
            max-height="600"
        >
            <el-table-column type="selection" width="55" fixed="left" />
            <el-table-column label="登录账号" width="140" prop="username">
                <template #default="scope">
                    <div class="font-16">{{ scope.row.username }}</div>
                </template>
            </el-table-column>

            <el-table-column label="联系人姓名" width="140" prop="nickname">
                <template #default="scope">
                    <div class="font-16">{{ scope.row.nickname }}</div>
                </template>
            </el-table-column>

            <el-table-column label="手机号" width="120" prop="mobile">
                <template #default="scope">
                    <div class="font-16">{{ scope.row.mobile }}</div>
                </template>
            </el-table-column>

            <el-table-column label="状态" prop="status" width="90">
                <template #default="scope">
                    <el-dropdown>
                        <el-tag class="pointer" :type="scope.row.status == 1 ? 'info' : 'success'">
                            <div class="flex flex-row gap-4 top-bottom-center">
                                {{ scope.row.status === 1 ? '停用' : '启用' }} <el-icon><ArrowDown /></el-icon>
                            </div>
                        </el-tag>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="changeStatus(0, scope.row)" v-if="scope.row.status === 1">
                                    启用
                                </el-dropdown-item>
                                <el-dropdown-item @click="changeStatus(1, scope.row)" v-if="scope.row.status === 0">
                                    停用
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </template>
            </el-table-column>
            <el-table-column label="角色" prop="roleName">
                <template #default="scope">
                    <div class="font-16">{{ scope.row.roleName.join(',') }}</div>
                </template>
            </el-table-column>
            <el-table-column v-if="isPlatManager || isYunwei" label="所属租户" prop="tenantName">
                <template #default="scope">
                    <div class="font-16">{{ scope.row.tenantName }}</div>
                </template>
            </el-table-column>
            <el-table-column label="所属组织" prop="orgNames">
                <template #default="scope">
                    <div class="font-16">{{ scope.row.orgNames.join(',') }}</div>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="90px" prop="action">
                <template #default="scope">
                    <div v-if="permissionService.isUserEditPermitted()">
                        <a class="r-margin-8 font-16 color-blue pointer" @click="editUser(scope.row)">编辑</a>
                    </div>
                    <div v-else>-</div>
                </template>
            </el-table-column>
        </el-table>
        <el-affix position="bottom" :offset="0">
            <div class="display-flex top-bottom-center justify-flex-end back-color-white tb-padding-16">
                <el-pagination
                    v-model:currentPage="pageInfo.page"
                    v-model:page-size="pageInfo.pageSize"
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageInfo.total"
                    @size-change="getData()"
                    @current-change="getData()"
                />
            </div>
        </el-affix>
        <UserUpdateDialog
            :visible="dialogVisible"
            :org-list="orgList"
            :type="updateType"
            :onClose="onUpdateDialogVisible"
            :user="currentUser"
            :current-org="currentOrg"
        />
    </div>
</template>

<style scoped>
:deep(.el-tooltip__trigger) {
    outline: none;
}

.org-user-table .el-table {
    --el-table-header-bg-color: var(--table-bg-2);
    --el-table-header-text-color: var(--main-black);
    --el-font-size-base: 16px;
}

.org-user-table .el-table th {
    height: 100px;
}

/* .org-user-table :deep(.el-table) th,
.org-user-table :deep(.el-table) td {
    height: 64px;
} */

.org-user-table :deep(.el-table) td {
    height: 64px;
}
</style>
