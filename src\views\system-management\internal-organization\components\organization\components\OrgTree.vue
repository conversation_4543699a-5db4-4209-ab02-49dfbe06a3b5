<script setup lang="ts">
import type { IOrgTreeItem } from '@/types/org'
import { Search } from '@element-plus/icons-vue'
import { computed, onBeforeUnmount, ref, watch } from 'vue'
import ORGPNG from '@/assets/images/header/org.png'
import { ElMessage, ElMessageBox, type TreeNode } from 'element-plus'
import systemService from '@/service/systemService'
import type Node from 'element-plus/es/components/tree/src/model/node.mjs'
import { debounce } from 'lodash'
import OrgUpdateDialog from './OrgUpdateDialog.vue'
import { flatten } from '@/utils/flatten'

const input = ref('')
const currentOrg = ref<IOrgTreeItem | null>(null)
const dialogVisible = ref(false)
const searching = ref(false)
const updateType = ref('add')
const exChildrenOrgList= ref<IOrgTreeItem[]>([])

const props = defineProps<{
    getOrgList: () => void
    orgList: IOrgTreeItem[]
    searchOrg: (v: string) => void
    searchList: IOrgTreeItem[] | null
    setCurrentOrg: (v: IOrgTreeItem) => void
}>()

const treeProps = {
    value: 'id',
    label: 'name',
    children: 'children',
}

const isShowSearchedList = computed(() => {
    return input.value !== ''
})

const addNewOrg = (org: IOrgTreeItem) => {
    updateType.value = 'add'
    currentOrg.value = org
    exChildrenOrgList.value=flatten(props.orgList)
    dialogVisible.value = true
}

const editOrg = (v: IOrgTreeItem) => {
    if (!v) return
    updateType.value = 'edit'
    currentOrg.value = v
    exChildrenOrgList.value=flatten(props.orgList).filter(item=>item.id !== currentOrg.value?.id && !item.parentIds?.includes(currentOrg.value?.id))
    dialogVisible.value = true
}

const deleteOrg = (v: IOrgTreeItem) => {
    if (!v) return
    const { id } = v || {}

    ElMessageBox.confirm('是否确认删除该组织?', '删除组织', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        systemService
            .orgRemove({ orgId: id })
            .then((res) => {
                const { errCode, errMsg } = res
                if (errCode === 0) {
                    ElMessage.success('删除成功')
                } else {
                    ElMessage.error(errMsg || '删除失败')
                }
            })
            .catch(() => {
                ElMessage.error('删除失败，请稍后再试')
            })
            .finally(() => {
                props.getOrgList()
                props.searchOrg(input.value)
            })
    })
}

const allowDrop = (draggingNode: TreeNode, dropNode: TreeNode, type: 'before' | 'after' | 'inner') => {
    if (draggingNode.level !== dropNode.level || type === 'inner') {
        return false
    } else {
        return true
    }
}

const editOrgSort = (node: Node, nextNode: Node) => {
    const id = node.data.id
    const toId = nextNode.data.id

    if (!id || !toId) return false
    systemService
        .orgSort({ id, toId })
        .then((res) => {
            const { errCode, errMsg } = res
            if (errCode === 0) {
                console.log('排序成功')
            } else {
                console.log(errMsg || '排序失败')
            }
        })
        .catch(() => {
            console.log('排序失败，请稍后再试')
        })
}

const onUpdateDialogVisible = (refresh?: boolean) => {
    dialogVisible.value = false
    if (refresh) {
        props.getOrgList()
        props.searchOrg(input.value)
    }
}

const onSearch = debounce((v: string) => {
    props.searchOrg(v)
}, 500)

const nodeClick = (node: IOrgTreeItem) => {
    props.setCurrentOrg(node)
}

watch(
    () => input.value,
    (value) => {
        if (value) {
            searching.value = true
            props.searchOrg(value)
        } else {
            props.searchOrg('')
        }
    }
)

watch(
    () => props.searchList,
    () => {
        searching.value = false
    }
)

// watch(
//     () => props.orgList,
//     (value) => {
//         if (value && value.length > 0) {
//             nodeClick(value[0])
//         }
//     }
// )

onBeforeUnmount(() => {
    onSearch.cancel()
})
</script>

<template>
    <div class="flex flex-column w-265 gap-16">
        <div class="organization-input">
            <el-input
                v-model="input"
                style="height: 36px"
                size="large"
                placeholder="搜索组织"
                :prefix-icon="Search"
                :clearable="true"
            />
        </div>
        <div class="flex flex-column gap-8 tree-list" v-show="!isShowSearchedList">
            <template v-for="org in orgList" :key="org.id">
                <div
                    class="h-36 flex flex-row gap-6 top-bottom-center all-padding-8 border border-radius-4"
                    @click="nodeClick(org)"
                >
                    <div class="flex top-bottom-center left-right-center w-16 h-16">
                        <img :src="ORGPNG" alt="org" class="width-100" />
                    </div>
                    <div class="font-14 lh-15 flex-1 text-nowrap text-ellipsis pointer">
                        {{ org.name }}
                    </div>
                    <el-icon :size="12" class="pointer color-blue--hover" @click="addNewOrg(org)">
                        <Plus />
                    </el-icon>
                </div>
                <el-tree
                    :data="org.children"
                    :props="treeProps"
                    :draggable="true"
                    :allow-drop="allowDrop"
                    @node-drop="editOrgSort"
                    @node-click="nodeClick"
                >
                    <template #default="{ node, data }: { node: Node; data: IOrgTreeItem }">
                        <el-popover placement="top" trigger="hover" popper-class="popover-org">
                            <template #default>
                                <div class="flex flex-row gap-10">
                                    <el-icon class="pointer color-blue--hover" @click="editOrg(data)">
                                        <Edit />
                                    </el-icon>
                                    <el-icon class="pointer color-blue--hover" @click="addNewOrg(data)">
                                        <Plus />
                                    </el-icon>
                                    <el-icon
                                        v-if="data.children.length === 0"
                                        class="pointer color-blue--hover"
                                        @click="deleteOrg(data)"
                                    >
                                        <Delete />
                                    </el-icon>
                                </div>
                            </template>
                            <template #reference>
                                <div class="width-100">
                                    <span>{{ node.label }}</span>
                                </div>
                            </template>
                        </el-popover>
                    </template>
                </el-tree>
            </template>
        </div>
        <div>
            <el-skeleton v-if="searching" />
            <template v-if="!searching">
                <div class="flex flex-column">
                    <div
                        v-for="item in searchList"
                        :key="item.id"
                        class="font-14 pointer tb-padding-12 lr-padding-12 back-tag-bg--hover border-radius-4"
                        @click="nodeClick(item)"
                    >
                        <el-popover placement="top" trigger="hover" popper-class="popover-org">
                            <template #default>
                                <div class="flex flex-row gap-10">
                                    <el-icon class="pointer color-blue--hover" @click="editOrg(item)">
                                        <Edit />
                                    </el-icon>
                                    <el-icon class="pointer color-blue--hover" @click="addNewOrg(item)">
                                        <Plus />
                                    </el-icon>
                                    <el-icon
                                        v-if="item.children.length === 0"
                                        class="pointer color-blue--hover"
                                        @click="deleteOrg(item)"
                                    >
                                        <Delete />
                                    </el-icon>
                                </div>
                            </template>
                            <template #reference>
                                <div class="width-100">
                                    {{ item.name }}
                                </div>
                            </template>
                        </el-popover>
                    </div>
                </div>
                <el-empty v-if="searchList && searchList.length === 0" :image-size="90" />
            </template>
        </div>
        <OrgUpdateDialog
            :visible="dialogVisible"
            :onClose="onUpdateDialogVisible"
            :org="currentOrg"
            :orgList="exChildrenOrgList"
            :type="updateType"
        />
    </div>
</template>

<style scoped>
.organization-input :deep(.el-input__inner) {
    height: 36px;
    font-size: 14px;
}
.tree-list :deep(.el-tree-node__content) {
    border-radius: 4px;
}

.el-tree {
    --el-tree-node-content-height: 36px;
}
</style>
<style lang="scss">
.el-popover.el-popper.popover-org {
    min-width: 20px !important;
    width: auto !important;
}
</style>
