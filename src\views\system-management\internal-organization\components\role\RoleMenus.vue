<script lang="ts" setup>
import { Search } from '@element-plus/icons-vue'
import { computed, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import systemService from '@/service/systemService'
import type { IRoleItem } from '@/types/role'
import type { IMenuResponse } from '@/types/menu'
import type { RootState } from '@/types/store'
import RoleForm from './components/RoleForm.vue'
import permissionService from '@/service/permissionService'

const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user || {}
    const { user } = account || {}
    return user
})

/***************角色的操作***************/
const roleList = ref<IRoleItem[]>([])
const checkedRole = ref<IRoleItem>({
    id: '',
    menuIds: [],
    roleId: '',
    roleName: '',
    scopeData: [],
    tenantId: '',
})
const roleFormVisible = ref(false)
const roleFormType = ref('add')
const searchVal = ref('')
const saveLoading = ref(false)
let timeoutId: number | null = null
watch(searchVal, (newVal: string) => {
    if (timeoutId) {
        clearTimeout(timeoutId)
    }
    timeoutId = window.setTimeout(async () => {
        console.log(newVal)
        getRoleList()
    }, 500)
})
let editItem = reactive<IRoleItem>({
    id: '',
    menuIds: [],
    roleId: '',
    roleName: '',
    scopeData: [],
    tenantId: '',
})
const getRoleList = async () => {
    let queryObj = {}
    if (searchVal.value) {
        queryObj = {
            roleName: searchVal.value,
        }
    }
    systemService.roleListByName(queryObj).then((res) => {
        const { errCode, data } = res
        if (errCode === 0) {
            // 将系统角色排到前面 目前判断系统角色依据 roleId === 'yuanqu_admin'
            data.sort((a, b) => {
                if (a.roleId === 'yuanqu_admin') return -1
                if (b.roleId === 'yuanqu_admin') return 1
                return 0
            })
            roleList.value = data
            if (checkedRole.value.id === '') {
                handleClickRole(data[0]) //默认选中第一项
            }
        } else {
            roleList.value = []
            ElMessage({
                type: 'error',
                message: '获取角色列表失败',
            })
        }
    })
}
const handleClickRole = (row: IRoleItem) => {
    checkedRole.value = row
}
const handleAddRole = () => {
    roleFormType.value = 'add'
    roleFormVisible.value = true
}
const handleEditRole = (row: IRoleItem) => {
    roleFormType.value = 'edit'
    editItem = row
    roleFormVisible.value = true
}
const handleDelRole = (row: IRoleItem) => {
    ElMessageBox.confirm('是否确认删除当前角色', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        let resRes = await systemService.roleRemove({ roleId: row.roleId })
        if (resRes.success === true) {
            ElMessage({
                type: 'success',
                message: '删除成功',
            })
            checkedRole.value.id = ''
            checkedRole.value.menuIds = []
            checkedRole.value.roleId = ''
            checkedRole.value.roleName = ''
            checkedRole.value.scopeData = []
            checkedRole.value.tenantId = ''
            if (searchVal.value) {
                // getRoleListByName(searchVal.value)
                getRoleList()
            } else {
                getRoleList()
            }
        }
    })
}
const handleCloseRoleForm = () => {
    roleFormVisible.value = false
    getRoleList()
}

/***************系统模块权限***************/
const isDisabled = ref(false)
// 获取所有有按钮权限的二级菜单
const menuLoadPermissionTreeList = ref<IMenuResponse[]>([])
const checkedMenuIds = ref<string[]>([])
const hasBtnMenuArr = computed(() => {
    return menuLoadPermissionTreeList.value.flatMap((level1) =>
        level1.children.filter((level2) => level2.children?.length > 0)
    )
})
watch(checkedRole, (newVal) => {
    isDisabled.value = (!userInfo.value?.role.includes('admin') && newVal.roleId === 'yuanqu_admin') || (!permissionService.isRoleEditPermitted())//非admin账号并且是系统角色 或者 没有编辑角色权限 ==> 开启禁用
    checkedMenuIds.value = checkedRole.value.menuIds
    if (newVal.roleId) {
        // 1.获取当前角色的菜单树
        systemService.menuLoadPermissionTree({ roleId: newVal.roleId }).then((res) => {
            const { data } = res
            menuLoadPermissionTreeList.value = data || []
        })
    }
})

function updateCheckedMenuIds(checkedMenuIds: string[], val: IMenuResponse) {
    // 检查 checkedMenuIds 是否包含 二级菜单中的任意一个
    const hasChildInChecked = val.children.some((child) => checkedMenuIds.includes(child.menuId))
    if (hasChildInChecked) {
        // 如果存在，则添加 val.menuId（如果尚未存在）
        if (!checkedMenuIds.includes(val.menuId)) {
            checkedMenuIds.push(val.menuId)
        }
    } else {
        // 如果都不存在，检查 checkedMenuIds 是否包含 val.menuId，存在则删除
        const index = checkedMenuIds.indexOf(val.menuId)
        if (index !== -1) {
            checkedMenuIds.splice(index, 1)
        }
    }
    hasBtnMenuArr.value.forEach((item) => {
        // 如果二级菜单不存在但是三级按钮却存在,那么删除三级存在的按钮
        if (!checkedMenuIds.includes(item.menuId)) {
            item.children.forEach((child) => {
                if (checkedMenuIds.includes(child.menuId)) {
                    const childIndex = checkedMenuIds.indexOf(child.menuId)
                    checkedMenuIds.splice(childIndex, 1)
                }
            })
        }
    })
    return checkedMenuIds
}

const changeRolePermission = (row: IMenuResponse, level: number) => {
    if (!checkedRole.value.id) return
    if (level === 2) {
        // 点击二级菜单权限
        // 如果checkedMenuIds中存在二级菜单(children)中的任意一个 添加父级
        // 如果同类目的二级菜单一个都不存在, 那么剔除这个类目的一级标题
        // 如果二级菜单没有勾选,那么剔除这个菜单下的按钮权限
        checkedMenuIds.value = updateCheckedMenuIds(checkedMenuIds.value, row)
    } else if (level === 3) {
        // 点击三级按钮权限
        // 如果checkedMenuIds中存在当前操作权限(row.children)中的任意一个 添加父级(该方案已抛弃)
        // const level3ChildInChecked = row.children.some((child) => checkedMenuIds.value.includes(child.menuId))
        // if (level3ChildInChecked) {
        //     if (!checkedMenuIds.value.includes(row.menuId)) {
        //         checkedMenuIds.value.push(row.menuId)
        //     }
        //     if (!checkedMenuIds.value.includes(row.parentId)) {
        //         checkedMenuIds.value.push(row.parentId)
        //     }
        // }
    }
    console.log('menuIds', checkedMenuIds.value)
}

const handleSave = async () => {
    if (!checkedRole.value.id) return
    saveLoading.value = true
    try {
        checkedRole.value.menuIds = checkedMenuIds.value
        let editRes = await systemService.roleEdit(checkedRole.value)
        saveLoading.value = false
        if (editRes.success === true) {
            ElMessage({
                type: 'success',
                message: '编辑成功',
            })
            getRoleList()
        }
    } catch (error) {
        console.log(error)
        saveLoading.value = false
    }
}

onMounted(() => {
    getRoleList()
})
</script>
<template>
    <div class="display-flex">
        <div class="role-box" style="height: 100vh">
            <div class="display-flex space-between top-bottom-center b-margin-12">
                <div class="title-left">角色列表</div>
                <div v-if="permissionService.isRoleAddPermitted()" class="title-right" @click="handleAddRole()">新增</div>
            </div>
            <el-input
                style="width: 100%; margin-bottom: 12px"
                placeholder="搜索角色"
                :prefix-icon="Search"
                v-model="searchVal"
            />
            <div class="role-content">
                <div
                    class="role-item"
                    :class="{ 'active-role': checkedRole.id === role.id }"
                    v-for="role in roleList"
                    :key="role.id"
                    @click="handleClickRole(role)"
                >
                    <div class="role-item-left">{{ role.roleName }}</div>
                    <!-- 如果是admin账号 -->
                    <div v-if="userInfo?.role.includes('admin')">
                        <div class="display-flex top-bottom-center">
                            <el-icon v-if="permissionService.isRoleEditPermitted()" @click="handleEditRole(role)"><Edit /></el-icon>
                            <el-icon v-if="permissionService.isRoleDeletePermitted()" style="margin-left: 5px" @click="handleDelRole(role)"><Delete /></el-icon>
                        </div>
                    </div>
                    <div v-else>
                        <div v-if="role.roleId === 'yuanqu_admin'" class="display-flex top-bottom-center system-role">
                            系统
                        </div>
                        <div v-else class="display-flex top-bottom-center">
                            <el-icon v-if="permissionService.isRoleEditPermitted()" @click="handleEditRole(role)"><Edit /></el-icon>
                            <el-icon v-if="permissionService.isRoleDeletePermitted()" style="margin-left: 5px" @click="handleDelRole(role)"><Delete /></el-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="permission-box">
            <div class="b-margin-12 display-flex top-bottom-center space-between">
                <div class="title-left">系统模块权限</div>
                <el-button :disabled="!userInfo?.role.includes('admin') && checkedRole.roleId === 'yuanqu_admin'" type="primary" :loading="saveLoading" @click="handleSave()">保存</el-button>
            </div>
            <el-table
                class="permission-table"
                :data="menuLoadPermissionTreeList"
                border
                style="width: 100%; margin-top: 20px"
                :hover="false"
                header-row-class-name="customer-header"
            >
                <el-table-column width="200" prop="name" label="所属模块">
                    <template #default="scope">
                        <div class="l-padding-14">{{ scope.row.name }}</div>
                    </template>
                </el-table-column>
                <el-table-column class-name="custom-column">
                    <template #header>
                        <div class="display-flex height-100">
                            <div style="width: 200px; padding-left: 14px; border-right: 1px solid #ebeef5;  align-content: center;">功能权限</div>
                            <div class="l-padding-14" style="align-content: center">操作权限</div>
                        </div>
                    </template>

                    <template #default="scope">
                        <!--------------------二级菜单(有)--------------------->
                        <div v-if="scope.row.children.length > 0">
                            <div
                                v-for="(SecondaryMenu, index) in scope.row.children"
                                :key="SecondaryMenu.id"
                                class="border-box h-34 display-flex height-100"
                                :class="index !== scope.row.children.length - 1 ? 'border-bottom' : ''"
                            >
                                <!-- 二级菜单 -->
                                <div
                                    class="l-padding-14"
                                    style="
                                        border-right: 1px solid var(--border-color);
                                        align-content: center;
                                        width: 200px;
                                    "
                                >
                                    <el-checkbox-group
                                        v-model="checkedMenuIds"
                                        :disabled="isDisabled"
                                        @change="changeRolePermission(scope.row, 2)"
                                    >
                                        <el-checkbox
                                            :id="SecondaryMenu.id"
                                            :label="SecondaryMenu.name"
                                            :value="SecondaryMenu.menuId"
                                        />
                                    </el-checkbox-group>
                                </div>
                                <!-- 三级菜单 -->
                                <div class="flex-1 l-padding-14">
                                    <!--------------------三级菜单(有)--------------------->
                                    <div v-if="SecondaryMenu.children.length > 0">
                                        <div
                                            class="display-inline r-margin-14"
                                            v-for="(actionBtn, idx) in SecondaryMenu.children"
                                            :key="idx"
                                        >
                                            <el-checkbox-group
                                                v-model="checkedMenuIds"
                                                :disabled="isDisabled"
                                                @change="changeRolePermission(SecondaryMenu, 3)"
                                            >
                                                <el-checkbox
                                                    :key="actionBtn.id"
                                                    :label="actionBtn.name"
                                                    :value="actionBtn.menuId"
                                                />
                                            </el-checkbox-group>
                                        </div>
                                    </div>
                                    <!--------------------三级菜单(无)--------------------->
                                    <div v-else>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--------------------二级菜单(无)--------------------->
                        <div
                            v-else
                            class="l-padding-14"
                            style="
                                width: 200px;
                                height: 100%;
                                align-content: center;
                                border-right: 1px solid var(--border-color);
                            "
                        >
                            <el-checkbox-group
                                v-model="checkedMenuIds"
                                :disabled="isDisabled"
                                @change="changeRolePermission(scope.row, 1)"
                            >
                                <el-checkbox :id="scope.row.id" :label="scope.row.name" :value="scope.row.menuId" :disabled="(scope.row.menuId==='home' && !userInfo?.role.includes('admin'))"/>
                            </el-checkbox-group>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <RoleForm
        :visible="roleFormVisible"
        :from="roleFormType"
        :editItem="editItem"
        @closeVisible="handleCloseRoleForm"
    ></RoleForm>
</template>
<style scoped lang="scss">
@use '@/styles/element-lead.scss';
.active-role {
    background-color: #f6f8fa;
}

.role-box {
    width: 297px;
    height: 100%;
    display: flex;
    flex-direction: column;
    // border-right: 1px solid #e8e8e8;
    padding: 16px;
    .title-left {
        font-size: 16px;
        color: var(--main-black);
    }
    .title-right {
        font-size: 14px;
        color: var(--main-blue-);
    }
    .role-content {
        flex: 1;
        overflow: auto;
        // background-color: pink;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        .role-item {
            // border: 1px solid red;
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 16px;
            margin-bottom: 12px;
            border-radius: 4px;
            &:hover {
                background-color: #f6f8fa;
            }
            .role-item-left {
                font-size: 14px;
                color: var(--main-black);
            }
            .system-role {
                font-size: 14px;
                color: var(--three-grey);
            }
            .role-item-right {
                font-size: 14px;
                color: var(--three-grey);
            }
        }
    }
}
.permission-box {
    flex: 1;
    padding: 16px;
    border-left: 2px solid var(--el-border-color-light);
    .permission-table {
        --el-table-row-hover-bg-color: transparent !important;
        .data-permission-column {
            position: absolute;
            top: 16px;
        }
    }
    .data-permission-title {
        width: 96px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        font-size: 16px;
        color: var(--main-black);
        border: 1px solid #e8e8e8;
    }
    .data-permission-content {
        flex: 1;
        text-align: center;
        align-content: center;
        border: 1px solid #e8e8e8;
    }
}
.border-bottom {
    border-bottom: 1px solid var(--border-color);
}

:deep(.customer-header) {
    .el-table__cell {
        .cell {
            padding-left: 14px;
        }
    }
}
:deep(.custom-column) {
    .cell {
        height: 100%;
        align-content: center;
        padding: 0px !important;
    }
}
:deep(.el-table__cell) {
    padding: 0;
    height: 64px;
    .cell {
        padding: 0px;
    }
}
.no-border-select {
    :deep(.el-select__wrapper) {
        box-shadow: none !important;
        border: none !important;
    }

    :deep(.el-select__inner) {
        box-shadow: none !important;
        border: none !important;
    }
    :deep(.el-select__wrapper:hover) {
        box-shadow: none !important;
        border: none !important;
        background-color: #fff !important;
    }
    :deep(.el-select__wrapper:focus) {
        box-shadow: none !important;
        border: none !important;
        background-color: #fff !important;
    }
}
</style>
