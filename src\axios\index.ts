import axios, { type AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { getItem } from '@/utils/storage'
import { _store } from '@/store'
import type { IAuthSysResponse } from '@/types/auth'
import type { IAxiosConfig, IAxiosInstance } from '@/types/axios'
import { addUrlPending, removeUrlPending } from './cancel.service'

// 常量声明
const BASIC_AUTH = 'Basic emhlbnFpOnpoZW5xaV9zZWNyZXQ='
const AUTH_HEADER = 'Shu-Auth'
const BEARER_PREFIX = 'Bearer '
const TOKEN_REFRESH_URL = '/api/zhenqi-auth/oauth/token'

// 类型声明
type FailedQueueItem = {
    resolve: (token: string) => void
    reject: (error: unknown) => void
}

let isRefreshing = false
let failedQueue: FailedQueueItem[] = []

const http: IAxiosInstance = axios.create({
    baseURL: '/',
    timeout: 60000,
    responseType: 'json',
    headers: {
        'Content-Type': 'application/json',
    },
})

/**
 * 处理请求队列
 * @param error - 错误对象
 * @param token - 新令牌
 */
const processQueue = (error: Error | null, token: string | null = null): void => {
    failedQueue.forEach(({ resolve, reject }) => {
        if (error) {
            reject(error)
        } else {
            resolve(token!)
        }
    })
    failedQueue = []
}

// 请求拦截器
http.interceptors.request.use(
    (config: IAxiosConfig) => {
        // 统一设置基础认证头
        config.headers = config.headers || {}
        config.headers.Authorization = BASIC_AUTH

        // 无需传入token
        if (config.unlessAuth) {
            return config
        }

        // 传入token
        // 优先使用传入的临时token, 自定义token
        if (config.headers.token) {
            config.headers[AUTH_HEADER] = `${BEARER_PREFIX}${config.headers.token}`
            delete config.headers.token // 使用后删除临时token
            return config
        }

        // 正常业务请求处理
        const accessToken = getItem('access_token')
        if (accessToken) {
            config.headers[AUTH_HEADER] = `${BEARER_PREFIX}${accessToken}`
        }

        config.hideError = config.hideError || false

        if (config.repeatCancel) {
            removeUrlPending(config) // 在请求开始前，对之前的请求做检查取消操作
            addUrlPending(config) // 将当前请求添加到 pending 中
        }

        return config
    },
    (error) => {
        ElMessage.error(error.message || '请求配置错误')
        return Promise.reject(error)
    }
)

// 响应拦截器
http.interceptors.response.use(
    (response: AxiosResponse) => {
        console.log('AxiosResponse')
        // 处理业务错误
        const { errMsg, errCode, data } = response.data
        const { hideError, repeatCancel, fullRes } = response.config as IAxiosConfig

        if (repeatCancel) {
            removeUrlPending(response) // 在请求结束后，移除本次请求
        }

        // 处理非常规状态码
        if (response.status !== 200) {
            ElMessage.error('服务异常，请稍后再试')
            return Promise.reject(new Error('服务异常'))
        }

        if (fullRes) {
            return response
        }

        if (hideError) {
            return response.data
        } else {
            if (errCode === 0) {
                return data
            } else {
                if (errMsg) {
                    ElMessage.error(errMsg)
                }
                return Promise.reject(new Error(errMsg || '业务错误'))
            }
        }
    },
    async (error) => {
        const originalRequest = error.config
        const { hideError } = originalRequest || {}

        // 处理取消的请求
        if (axios.isCancel(error)) {
            return Promise.reject({
                isCanceled: true,
                message: '请求已被取消',
            })
        }

        // 处理401未授权
        if (error.response?.status === 401 && !originalRequest._retry) {
            console.log('处理401')
            const refreshToken = getItem('refresh_token')

            // 无刷新令牌直接退出
            if (!refreshToken) {
                console.log('无刷新令牌直接退出')
                _store.dispatch('auth/logout')
                return Promise.reject(error)
            }

            // 标记重试防止循环
            if (originalRequest._retry) {
                _store.dispatch('auth/logout')
                return Promise.reject(error)
            }

            // 处理并发请求
            if (isRefreshing) {
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject })
                })
                    .then((token) => {
                        originalRequest.headers![AUTH_HEADER] = `${BEARER_PREFIX}${token}`
                        return http(originalRequest)
                    })
                    .catch((reject) => reject)
            }

            originalRequest._retry = true
            isRefreshing = true

            try {
                // 刷新令牌请求
                const { data } = await axios.post<IAuthSysResponse>(
                    TOKEN_REFRESH_URL,
                    {
                        refresh_token: refreshToken,
                        grant_type: 'refresh_token',
                    },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            Authorization: BASIC_AUTH,
                        },
                    }
                )

                if (data?.errCode === 0 && data.data) {
                    const { access_token } = data.data
                    _store.dispatch('auth/loginSuccess', data.data)

                    // 更新全局header和原始请求header
                    http.defaults.headers.common[AUTH_HEADER] = `${BEARER_PREFIX}${access_token}`
                    originalRequest.headers![AUTH_HEADER] = `${BEARER_PREFIX}${access_token}`

                    // 重试原始请求并处理队列
                    processQueue(null, access_token)
                    return http(originalRequest)
                }

                throw new Error('令牌刷新失败')
            } catch (e) {
                // 刷新失败处理
                console.log('刷新令牌失败')
                _store.dispatch('auth/logout')
                return Promise.reject(e)
            } finally {
                isRefreshing = false
            }
        }

        // 其他错误处理
        const errorMessage = error.response?.data?.errMsg || error.message || '未知错误'
        if (error.response?.status === 403) {
            ElMessage.error(errorMessage)
            return
        }

        if (error.response?.status === 500) {
            if (hideError) {
                return Promise.reject('系统错误，请稍后再试')
            } else {
                return ElMessage.error('系统错误，请稍后再试')
            }
        }

        if (!error.config?.silent && !hideError) {
            // 可配置静默错误
            if (window.timeoutTm) {
                clearTimeout(window.timeoutTm)
            }
            return window.timeoutTm = setTimeout(() => {
                ElMessage.error(error.message)
            }, 500)
        }

        return Promise.reject(error)
    }
)

http.get = (url: string, config: IAxiosConfig) => {
    const { params, ...restConfig } = config || {}
    let finalUrl = url

    // 如果有params，将其转换为查询字符串并拼接到 URL
    if (params) {
        const queryParams = new URLSearchParams(
            Object.entries(params).reduce(
                (acc, [key, value]) => {
                    if (value === undefined || value === null || Number.isNaN(value)) {
                        value = ''
                    }

                    acc[key] = String(value) // 确保所有值都是字符串
                    return acc
                },
                {} as Record<string, string>
            )
        ).toString()

        if (queryParams) {
            finalUrl = `${url}?${queryParams}`
        } else {
            finalUrl = url
        }
    }

    return http.request({ ...restConfig, method: 'get', url: finalUrl })
}
http.post = (url, data, config) => http.request({ ...config, method: 'post', url, data })
http.put = (url, data, config) => http.request({ ...config, method: 'put', url, data })
http.delete = (url, config) => http.request({ ...config, method: 'delete', url })

export default http
