<template>
    <div ref="mainContentRef" style="background-color: #f7f7f7">
        <div ref="searchContentRef" style="background-color: #fff; padding: 16px">
            <div class="t-margin-10 display-flex" style="height: 40px">
                <!-- 搜索框 -->
                <el-input placeholder="请输入企业名称或者主营产品" v-model="input" style="width: 30%">
                    <template #prefix>
                        <Icon icon="icon-a-chazhaoqiye"></Icon>
                    </template>
                </el-input>
                <div class="l-margin-10 search-btn pointer font-16" @click="search">搜 索</div>
            </div>
            <div class="t-margin-20">
                <FactorySearchFilter />
            </div>
        </div>
        <div class="t-margin-16" style="background-color: #fff; padding: 16px; box-sizing: border-box">
            <div ref="actionBarContentRef" class="display-flex top-bottom-center">
                <div class="r-margin-10 font-navbar">
                    共
                    <span class="color-blue lr-padding-2 font-weight-600">
                        {{ totalnum > 1000000 ? '100w+' : totalnum }}
                    </span>
                    个结果
                </div>
                <div class="color-border">|</div>
                <div class="l-margin-10 font-navbar">
                    已选
                    <span class="color-blue lr-padding-2 font-weight-600">{{ selectedLength }}</span>
                    个
                </div>
                <el-dropdown v-if="permissionService.isTransferNewLeadPermitted()" class="l-margin-10">
                    <el-button>
                        转CRM
                        <el-icon class="el-icon--right">
                            <CaretBottom />
                        </el-icon>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="batchTransfer()">转移所选</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <div class="display-flex t-margin-20 b-margin-20">
                <el-table
                    ref="tableRef"
                    :data="companyTableData"
                    v-loading="tableLoading"
                    :height="tableHeight+'px'"
                    @selection-change="selectionChange"
                    show-overflow-tooltip
                    empty-text="暂无数据"
                    row-key="socialCreditCode"
                    :header-cell-style="{ background: '#F5F7FA', color: '#333' }"
                >
                    <el-table-column type="selection" width="64" reserve-selection></el-table-column>
                    <el-table-column label="企业信息" style="width: 50%">
                        <template #default="scope">
                            <div class="t-margin-6 display-flex font-18 pointer">{{ scope.row.companyName }}</div>
                            <div class="display-flex t-margin-6 flex-wrap">
                                <div
                                    class="l-margin-4 t-margin-4"
                                    v-for="(tagItem, i) in scope.row.companyTags"
                                    :key="i"
                                >
                                    <el-tag class="font-14" effect="plain" :type="getTagClass(tagItem.categoryCode)">{{
                                        tagItem.tagName
                                    }}</el-tag>
                                </div>
                            </div>
                            <div class="t-margin-6 display-flex font-16">
                                <div>{{ scope.row.legalperson }}</div>
                                <div class="r-margin-6 l-margin-6">|</div>
                                <div>{{ scope.row.esdate }}</div>
                                <div class="r-margin-6 l-margin-6">|</div>
                                <div>{{ scope.row.regCapDisplay }}</div>
                            </div>
                            <div class="t-margin-6 font-14 display-flex color-three-grey">
                                地区：{{ scope.row.companyArea }}
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="生产信息" style="width: 50%">
                        <template #default="scope">
                            <div class="t-margin-10 display-flex font-16">
                                <div class="color-three-grey">厂房面积:</div>
                                <div>{{ scope.row.plantArea ? scope.row.plantArea : '-' }}</div>
                            </div>
                            <div class="t-margin-10 display-flex font-16">
                                <div class="color-three-grey">年营业额:</div>
                                <div>{{ scope.row.annualSale ? scope.row.annualSale : '-' }}</div>
                            </div>
                            <div class="t-margin-10 display-flex font-16">
                                <div class="color-three-grey">主营产品:</div>
                                <div class="text-ellipsis" :title="scope.row.b2bProduct">
                                    {{ scope.row.b2bProduct }}
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-affix position="bottom" :offset="0">
                <div class="pagination-bar">
                    <el-pagination
                        v-model:currentPage="pageInfo.page"
                        v-model:page-size="pageInfo.pageSize"
                        :total="pageInfo.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @change="pageChange"
                    />
                </div>
            </el-affix>
        </div>
        <TransferCrmDialog
            v-model:visible="transferCrmDialogVisible"
            :selected="multipleSelection"
            :success="transferCrmSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
import Icon from '@/components/common/Icon.vue'
import { ref, computed, reactive, onMounted, watch } from 'vue'
import type { FilterResultValue, ISearchBidAndFactoryParams, ISearchFactoryItem, ICompanyInfo } from '@/types/company'
import FactorySearchFilter from './components/FactorySearchFilter.vue'
import type { RootState } from '@/types/store'
import { useStore } from 'vuex'
import { formatBidFilters } from '@/utils/enterprise/filters'
import aicService from '@/service/aicService'
import TransferCrmDialog from '@/components/transfer-crm-dialog/TransferCrmDialog.vue'
import permissionService from '@/service/permissionService'

const mainContentRef = ref<HTMLDivElement | null>(null)
const searchContentRef = ref<HTMLDivElement | null>(null)
const actionBarContentRef = ref<HTMLDivElement | null>(null)
const tableHeight = ref(500)
const getTableHeight = () => {
    if (mainContentRef.value && searchContentRef.value && actionBarContentRef.value) {    
        tableHeight.value = mainContentRef.value.clientHeight - searchContentRef.value.clientHeight - actionBarContentRef.value.clientHeight - 32 - 16 - 16 - 16 - 16 - 16
    }
}
const transferCrmDialogVisible = ref(false)
const tableLoading = ref(false)
const totalnum = ref(0)
const tableRef = ref()
const pageInfo = reactive({
    page: 1,
    pageSize: 20,
    total: totalnum,
})

const pageChange = (currentPage: number, currentPagesize: number) => {
    searchFactoryParams.value.page = currentPage
    searchFactoryParams.value.pageSize = currentPagesize
    getFactoryInfo(searchFactoryParams.value)
}
const multipleSelection = ref<ICompanyInfo[]>([])
const store = useStore<RootState>()
const checkIds = ref<string[]>([])
const input = ref('')
const selectedData = ref<ISearchFactoryItem[]>([])
const companyTableData = ref<ISearchFactoryItem[]>([])
const searchFactoryParams = ref<ISearchBidAndFactoryParams>({
    annualSale: [],
    area: {},
    contactType_hasMore: [],
    esDate_customer: [],
    keyword: '',
    matchType: '',
    model: '',
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
    plantArea: [],
    regCapUnify_customer: [],
    scope: '',
    sortBy: 0,
    staffs: [],
})
const factoryFilterParams = ref<{ [x: string]: FilterResultValue }>({})
const factorySearchParams = computed(() => {
    const { factoryFilterParams } = store.state.enterprise
    return factoryFilterParams
})

const getTagClass = (code: string) => {
    const codeStatusList = [
        { code: '001', status: 'success' },
        { code: '002', status: 'info' },
        { code: '003', status: 'success' },
        { code: '004', status: 'info' },
        { code: '005', status: 'primary' },
        { code: '006', status: 'danger' },
        { code: '007', status: 'warning' },
    ]

    const target = codeStatusList.find((item) => item.code === code)
    if (!target) return 'danger'
    return target.status
}

const getFactoryInfo = (Params: ISearchBidAndFactoryParams) => {
    tableLoading.value = true
    Params.page = pageInfo.page
    Params.pageSize = pageInfo.pageSize
    Params.sortBy = 0
    aicService
        .searchFactory(Params)
        .then((response) => {
            console.log('response', response)
            tableLoading.value = false
            const { errCode } = response
            if (errCode === 0) {
                companyTableData.value = response.data
                totalnum.value = response.total
            } else {
                companyTableData.value = []
                totalnum.value = 0
            }
        })
        .catch((error) => {
            console.log('error', error)

            const { isCanceled } = error
            if (!isCanceled) {
                tableLoading.value = false
                companyTableData.value = []
                totalnum.value = 0
            }
        })
}

const search = () => {
    // 增加搜索条件
    console.log('点击了搜索按钮', input.value)
    searchFactoryParams.value = { ...searchFactoryParams.value, ...factoryFilterParams.value, keyword: input.value }
    getFactoryInfo(searchFactoryParams.value)
}

const batchTransfer = () => {
    transferCrmDialogVisible.value = true
    console.log('点击了批量转移按钮', selectedData.value)
}

// 清除勾选状态
const transferCrmSuccess = () => {
    if (tableRef.value) {
        tableRef.value.clearSelection()
    }
}

const selectionChange = (val: ICompanyInfo[]) => {
    selectedData.value = val
    multipleSelection.value = val
    checkIds.value = val.map((i) => {
        return i.socialCreditCode
    })
    console.log('val', val)
    console.log('checkIds', checkIds.value)
}

const selectedLength = computed(() => selectedData.value.length)

watch(
    () => factorySearchParams,
    (value) => {
        const formattedData = formatBidFilters(value.value)
        factoryFilterParams.value = formattedData
        getFactoryInfo({ ...searchFactoryParams.value, ...formattedData })
    },
    { deep: true }
)

onMounted(() => {
    // getFactoryInfo(searchFactoryParams.value)
    getTableHeight()
})
</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.search-btn {
    background: #1966ff;
    border-radius: 4px;
    color: #fff;
    width: 68px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pagination-bar {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    background-color: #fff;
    padding-top: 16px;
    padding-bottom: 16px;
}
</style>
